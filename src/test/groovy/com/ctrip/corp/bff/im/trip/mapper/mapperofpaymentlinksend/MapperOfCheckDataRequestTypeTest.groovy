package com.ctrip.corp.bff.im.trip.mapper.mapperofpaymentlinksend

import com.ctrip.corp.bff.framework.template.entity.MapString
import com.ctrip.corp.bff.framework.template.entity.PosEnum
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.contract.vo.EmailInfoVO
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.mcinfo.trip.contract.PaymentLinkSendRequestVO
import com.ctrip.corp.bff.tools.contract.CheckDataRequestType
import com.ctrip.corp.bff.tools.contract.DataInfo
import spock.lang.*

class MapperOfCheckDataRequestTypeTest extends Specification {

    MapperOfCheckDataRequestType mapper = new MapperOfCheckDataRequestType()

    def "test convert with valid input"() {
        given: "A valid PaymentLinkSendRequestVO with email info"
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl",
            "userAgent", "deviceType", "language", "requestId", SourceFrom.H5,
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc",
            PosEnum.CHINA, "timezoneOffsetMinutesNew",
            [new MapString("key", "value")], [new MapString("key", "value")],
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE,
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )

        def emailInfo = new EmailInfoVO()
        emailInfo.setTransferEmail("<EMAIL>")
        emailInfo.setEmail("<EMAIL>")

        def paymentLinkSendRequestVO = new PaymentLinkSendRequestVO()
        paymentLinkSendRequestVO.setRequestHeader(templateSoaRequestType)
        paymentLinkSendRequestVO.setEmailInfo(emailInfo)
        paymentLinkSendRequestVO.setOrderId("12345")

        def input = Tuple1.of(paymentLinkSendRequestVO)

        when: "Converting the input"
        CheckDataRequestType result = mapper.convert(input)

        then: "The result should be properly mapped"
        result != null
        result.getIntegrationSoaRequestType() != null
        result.getDataInfoList() != null
        result.getDataInfoList().size() == 1

        def dataInfo = result.getDataInfoList().get(0)
        dataInfo.getData() == "<EMAIL>"
        dataInfo.getType() == "email"
        dataInfo.getKey() == "<EMAIL>"
    }

    def "test convert with null input should throw exception"() {
        when: "Converting null input"
        mapper.convert(null)

        then: "Should throw NullPointerException"
        thrown(NullPointerException)
    }

    def "test convert with null PaymentLinkSendRequestVO should throw exception"() {
        given: "A Tuple1 with null PaymentLinkSendRequestVO"
        def input = Tuple1.of(null)

        when: "Converting the input"
        mapper.convert(input)

        then: "Should throw NullPointerException"
        thrown(NullPointerException)
    }

    def "test convert with null emailInfo should throw exception"() {
        given: "A PaymentLinkSendRequestVO with null emailInfo"
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl",
            "userAgent", "deviceType", "language", "requestId", SourceFrom.H5,
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc",
            PosEnum.CHINA, "timezoneOffsetMinutesNew",
            [new MapString("key", "value")], [new MapString("key", "value")],
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE,
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )

        def paymentLinkSendRequestVO = new PaymentLinkSendRequestVO()
        paymentLinkSendRequestVO.setRequestHeader(templateSoaRequestType)
        paymentLinkSendRequestVO.setEmailInfo(null)  // null emailInfo

        def input = Tuple1.of(paymentLinkSendRequestVO)

        when: "Converting the input"
        mapper.convert(input)

        then: "Should throw NullPointerException"
        thrown(NullPointerException)
    }

    def "test convert with null transferEmail"() {
        given: "A PaymentLinkSendRequestVO with emailInfo having null transferEmail"
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl",
            "userAgent", "deviceType", "language", "requestId", SourceFrom.Native,
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc",
            PosEnum.CHINA, "timezoneOffsetMinutesNew",
            [new MapString("key", "value")], [new MapString("key", "value")],
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE,
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )

        def emailInfo = new EmailInfoVO()
        emailInfo.setTransferEmail(null)  // null transferEmail
        emailInfo.setEmail("<EMAIL>")

        def paymentLinkSendRequestVO = new PaymentLinkSendRequestVO()
        paymentLinkSendRequestVO.setRequestHeader(templateSoaRequestType)
        paymentLinkSendRequestVO.setEmailInfo(emailInfo)

        def input = Tuple1.of(paymentLinkSendRequestVO)

        when: "Converting the input"
        CheckDataRequestType result = mapper.convert(input)

        then: "The result should handle null transferEmail"
        result != null
        result.getDataInfoList() != null
        result.getDataInfoList().size() == 1

        def dataInfo = result.getDataInfoList().get(0)
        dataInfo.getData() == null
        dataInfo.getType() == "email"
        dataInfo.getKey() == null
    }

    def "test check method always returns null"() {
        given: "Any input"
        def input = Tuple1.of(Mock(PaymentLinkSendRequestVO))

        when: "Calling check method"
        ParamCheckResult result = mapper.check(input)

        then: "Should always return null"
        result == null
    }

    def "test check method with null input"() {
        when: "Calling check method with null"
        ParamCheckResult result = mapper.check(null)

        then: "Should return null"
        result == null
    }

    def "test map method integration"() {
        given: "A valid PaymentLinkSendRequestVO"
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl",
            "userAgent", "deviceType", "language", "requestId", SourceFrom.CRN,
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc",
            PosEnum.CHINA, "timezoneOffsetMinutesNew",
            [new MapString("key", "value")], [new MapString("key", "value")],
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE,
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )

        def emailInfo = new EmailInfoVO()
        emailInfo.setTransferEmail("<EMAIL>")
        emailInfo.setEmail("<EMAIL>")

        def paymentLinkSendRequestVO = new PaymentLinkSendRequestVO()
        paymentLinkSendRequestVO.setRequestHeader(templateSoaRequestType)
        paymentLinkSendRequestVO.setEmailInfo(emailInfo)
        paymentLinkSendRequestVO.setOrderId("67890")

        def input = Tuple1.of(paymentLinkSendRequestVO)

        when: "Calling map method"
        CheckDataRequestType result = mapper.map(input)

        then: "Should return properly mapped result"
        result != null
        result.getIntegrationSoaRequestType() != null
        result.getDataInfoList() != null
        result.getDataInfoList().size() == 1

        def dataInfo = result.getDataInfoList().get(0)
        dataInfo.getData() == "<EMAIL>"
        dataInfo.getType() == "email"
        dataInfo.getKey() == "<EMAIL>"
    }
}