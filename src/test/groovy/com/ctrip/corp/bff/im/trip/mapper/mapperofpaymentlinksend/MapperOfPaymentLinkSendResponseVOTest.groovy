package com.ctrip.corp.bff.im.trip.mapper.mapperofpaymentlinksend

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.corp.bff.im.trip.common.enums.error.PaymentLinkSendErrorEnum
import com.ctrip.corp.bff.mcinfo.trip.contract.PaymentLinkSendResponseVO
import com.ctrip.corp.order.messagecenter.sender.contract.ManualSendMessageResponseType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderBasicInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderDetailInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderIndexGetResponseType
import mockit.Mock
import mockit.MockUp
import spock.lang.*


class MapperOfPaymentLinkSendResponseVOTest extends Specification {
    
    MapperOfPaymentLinkSendResponseVO mapper = new MapperOfPaymentLinkSendResponseVO()

    def "test convert with successful manual send message and WaitPay order status"() {
        given: "A valid input with WaitPay order status and successful response"
        def orderBasicInfo = new OrderBasicInfoType()
        orderBasicInfo.setOrderStatus("WaitPay")
        
        def orderDetailInfo = new OrderDetailInfoType()
        orderDetailInfo.setBasicInfo(orderBasicInfo)
        
        def orderIndexGetResponseType = new OrderIndexGetResponseType()
        orderIndexGetResponseType.setOrderDetailInfo(orderDetailInfo)
        
        def manualSendMessageResponseType = new ManualSendMessageResponseType()
        manualSendMessageResponseType.setResponseCode(20000)
        
        def input = Tuple2.of(orderIndexGetResponseType, manualSendMessageResponseType)
        
        // Mock BFFSharkUtil
        new MockUp<BFFSharkUtil>() {
            @Mock
            String getSharkValue(String key) {
                if (key == "trip.biz.bff.paymentlinksend.success") {
                    return "Send success message"
                }
                return "Default message"
            }
        }

        when: "Converting the input"
        PaymentLinkSendResponseVO result = mapper.convert(input)

        then: "The result should be properly mapped"
        result != null
        result.getSendResult() == "SUCCESS"
        result.getSendResultTip() == "Send success message"
    }

    def "test convert with non-WaitPay order status"() {
        given: "A valid input with non-WaitPay order status"
        def orderBasicInfo = new OrderBasicInfoType()
        orderBasicInfo.setOrderStatus("Paid")
        
        def orderDetailInfo = new OrderDetailInfoType()
        orderDetailInfo.setBasicInfo(orderBasicInfo)
        
        def orderIndexGetResponseType = new OrderIndexGetResponseType()
        orderIndexGetResponseType.setOrderDetailInfo(orderDetailInfo)
        
        def manualSendMessageResponseType = new ManualSendMessageResponseType()
        manualSendMessageResponseType.setResponseCode(20000)
        
        def input = Tuple2.of(orderIndexGetResponseType, manualSendMessageResponseType)
        
        // Mock BFFSharkUtil
        new MockUp<BFFSharkUtil>() {
            @Mock
            String getSharkValue(String key) {
                if (key == "trip.biz.bff.paymentlinksend.error.notwaitpay") {
                    return "Order status not wait pay"
                }
                return "Default message"
            }
        }

        when: "Converting the input"
        PaymentLinkSendResponseVO result = mapper.convert(input)

        then: "The result should indicate disabled send"
        result != null
        result.getSendResult() == "DISABLE_SEND"
        result.getSendResultTip() == "Order status not wait pay"
    }

    def "test convert with WaitPay order status but failed manual send message"() {
        given: "A valid input with WaitPay order status but failed response"
        def orderBasicInfo = new OrderBasicInfoType()
        orderBasicInfo.setOrderStatus("WaitPay")
        
        def orderDetailInfo = new OrderDetailInfoType()
        orderDetailInfo.setBasicInfo(orderBasicInfo)
        
        def orderIndexGetResponseType = new OrderIndexGetResponseType()
        orderIndexGetResponseType.setOrderDetailInfo(orderDetailInfo)
        
        def manualSendMessageResponseType = new ManualSendMessageResponseType()
        manualSendMessageResponseType.setResponseCode(50000)
        
        def input = Tuple2.of(orderIndexGetResponseType, manualSendMessageResponseType)

        when: "Converting the input"
        PaymentLinkSendResponseVO result = mapper.convert(input)

        then: "The result should have null values for failed send"
        result != null
        result.getSendResult() == null
        result.getSendResultTip() == null
    }

    def "test convert with null ManualSendMessageResponseType"() {
        given: "A valid input with null ManualSendMessageResponseType"
        def orderBasicInfo = new OrderBasicInfoType()
        orderBasicInfo.setOrderStatus("WaitPay")
        
        def orderDetailInfo = new OrderDetailInfoType()
        orderDetailInfo.setBasicInfo(orderBasicInfo)
        
        def orderIndexGetResponseType = new OrderIndexGetResponseType()
        orderIndexGetResponseType.setOrderDetailInfo(orderDetailInfo)
        
        def input = Tuple2.of(orderIndexGetResponseType, null)

        when: "Converting the input"
        PaymentLinkSendResponseVO result = mapper.convert(input)

        then: "The result should handle null response gracefully"
        result != null
        result.getSendResult() == null
        result.getSendResultTip() == null
    }

    def "test convert with null OrderIndexGetResponseType"() {
        given: "A valid input with null OrderIndexGetResponseType"
        def manualSendMessageResponseType = new ManualSendMessageResponseType()
        manualSendMessageResponseType.setResponseCode(20000)
        
        def input = Tuple2.of(null, manualSendMessageResponseType)
        
        // Mock BFFSharkUtil
        new MockUp<BFFSharkUtil>() {
            @Mock
            String getSharkValue(String key) {
                if (key == "trip.biz.bff.paymentlinksend.error.notwaitpay") {
                    return "Order status not wait pay"
                }
                return "Default message"
            }
        }

        when: "Converting the input"
        PaymentLinkSendResponseVO result = mapper.convert(input)

        then: "The result should indicate disabled send"
        result != null
        result.getSendResult() == "DISABLE_SEND"
        result.getSendResultTip() == "Order status not wait pay"
    }

    def "test convert with null input should throw exception"() {
        when: "Converting null input"
        mapper.convert(null)

        then: "Should throw NullPointerException"
        thrown(NullPointerException)
    }

    def "test check method with non-WaitPay order status should return null"() {
        given: "A valid input with non-WaitPay order status"
        def orderBasicInfo = new OrderBasicInfoType()
        orderBasicInfo.setOrderStatus("Paid")
        
        def orderDetailInfo = new OrderDetailInfoType()
        orderDetailInfo.setBasicInfo(orderBasicInfo)
        
        def orderIndexGetResponseType = new OrderIndexGetResponseType()
        orderIndexGetResponseType.setOrderDetailInfo(orderDetailInfo)
        
        def manualSendMessageResponseType = new ManualSendMessageResponseType()
        manualSendMessageResponseType.setResponseCode(50000)
        
        def input = Tuple2.of(orderIndexGetResponseType, manualSendMessageResponseType)

        when: "Calling check method"
        ParamCheckResult result = mapper.check(input)

        then: "Should return null because order doesn't need manual send"
        result == null
    }

    def "test check method with WaitPay order status and successful response should return null"() {
        given: "A valid input with WaitPay order status and successful response"
        def orderBasicInfo = new OrderBasicInfoType()
        orderBasicInfo.setOrderStatus("WaitPay")
        
        def orderDetailInfo = new OrderDetailInfoType()
        orderDetailInfo.setBasicInfo(orderBasicInfo)
        
        def orderIndexGetResponseType = new OrderIndexGetResponseType()
        orderIndexGetResponseType.setOrderDetailInfo(orderDetailInfo)
        
        def manualSendMessageResponseType = new ManualSendMessageResponseType()
        manualSendMessageResponseType.setResponseCode(20000)
        
        def input = Tuple2.of(orderIndexGetResponseType, manualSendMessageResponseType)

        when: "Calling check method"
        ParamCheckResult result = mapper.check(input)

        then: "Should return null"
        result == null
    }
}