package com.ctrip.corp.bff.im.trip.common.util

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil
import com.ctrip.corp.bff.im.trip.common.enums.error.PaymentLinkSendErrorEnum
import com.ctrip.corp.bff.tools.contract.CheckDataResponseType
import com.ctrip.corp.bff.tools.contract.DataCheckResult
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderBasicInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderDetailInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderIndexGetResponseType
import spock.lang.Specification
import spock.lang.Unroll

class PaymentLinkSendUtilTest extends Specification {

    @Unroll
    def "test needManualSendMessage with orderStatus: #orderStatus should return #expectedResult"() {
        given: "Create order response with specific order status"
        def orderBasicInfo = new OrderBasicInfoType()
        orderBasicInfo.setOrderStatus(orderStatus)

        def orderDetailInfo = new OrderDetailInfoType()
        orderDetailInfo.setBasicInfo(orderBasicInfo)

        def orderIndexResponse = new OrderIndexGetResponseType()
        orderIndexResponse.setOrderDetailInfo(orderDetailInfo)

        when: "Call needManualSendMessage"
        def result = PaymentLinkSendUtil.needManualSendMessage(orderIndexResponse)

        then: "Should return expected result"
        result == expectedResult

        where:
        orderStatus | expectedResult
        "WaitPay"   | true
        "waitpay"   | true
        "WAITPAY"   | true
        "Paid"      | false
        "Cancelled" | false
        "Refunded"  | false
        null        | false
        ""          | false
        "   "       | false
    }

    def "test needManualSendMessage with null orderIndexGetResponseType"() {
        when: "Call needManualSendMessage with null"
        def result = PaymentLinkSendUtil.needManualSendMessage(null)

        then: "Should return false"
        result == false
    }

    def "test needManualSendMessage with null orderDetailInfo"() {
        given: "Create order response with null orderDetailInfo"
        def orderIndexResponse = new OrderIndexGetResponseType()
        orderIndexResponse.setOrderDetailInfo(null)

        when: "Call needManualSendMessage"
        def result = PaymentLinkSendUtil.needManualSendMessage(orderIndexResponse)

        then: "Should return false"
        result == false
    }

    def "test needManualSendMessage with null basicInfo"() {
        given: "Create order response with null basicInfo"
        def orderDetailInfo = new OrderDetailInfoType()
        orderDetailInfo.setBasicInfo(null)

        def orderIndexResponse = new OrderIndexGetResponseType()
        orderIndexResponse.setOrderDetailInfo(orderDetailInfo)

        when: "Call needManualSendMessage"
        def result = PaymentLinkSendUtil.needManualSendMessage(orderIndexResponse)

        then: "Should return false"
        result == false
    }

    def "test checkData with null checkDataResponseType"() {
        when: "Call checkData with null"
        PaymentLinkSendUtil.checkData(null)

        then: "Should not throw exception"
        noExceptionThrown()
    }

    def "test checkData with empty resultList"() {
        given: "Create checkDataResponseType with empty resultList"
        def checkDataResponse = new CheckDataResponseType()
        checkDataResponse.setResultList([])

        when: "Call checkData"
        PaymentLinkSendUtil.checkData(checkDataResponse)

        then: "Should not throw exception"
        noExceptionThrown()
    }

    def "test checkData with null resultList"() {
        given: "Create checkDataResponseType with null resultList"
        def checkDataResponse = new CheckDataResponseType()
        checkDataResponse.setResultList(null)

        when: "Call checkData"
        PaymentLinkSendUtil.checkData(checkDataResponse)

        then: "Should not throw exception"
        noExceptionThrown()
    }

    def "test checkData with null checkResult in list"() {
        given: "Create checkDataResponseType with null checkResult"
        def checkDataResponse = new CheckDataResponseType()
        checkDataResponse.setResultList([null])

        when: "Call checkData"
        PaymentLinkSendUtil.checkData(checkDataResponse)

        then: "Should not throw exception"
        noExceptionThrown()
    }

    def "test checkData with successful check result"() {
        given: "Create checkDataResponseType with successful check result"
        def checkResult = new DataCheckResult()
        checkResult.setCheckResult(BooleanUtil.parseStr(true))
        checkResult.setFriendlyMessage("Success message")

        def checkDataResponse = new CheckDataResponseType()
        checkDataResponse.setResultList([checkResult])

        when: "Call checkData"
        PaymentLinkSendUtil.checkData(checkDataResponse)

        then: "Should not throw exception"
        noExceptionThrown()
    }


    def "test checkData with multiple check results - one failed"() {
        given: "Create checkDataResponseType with multiple check results"
        def successResult = new DataCheckResult()
        successResult.setCheckResult(BooleanUtil.parseStr(true))
        successResult.setFriendlyMessage("Success message")

        def failedResult = new DataCheckResult()
        failedResult.setCheckResult(BooleanUtil.parseStr(false))
        failedResult.setFriendlyMessage("Validation error")

        def checkDataResponse = new CheckDataResponseType()
        checkDataResponse.setResultList([successResult, failedResult])

        when: "Call checkData"
        PaymentLinkSendUtil.checkData(checkDataResponse)

        then: "Should throw BusinessException for the failed result"
        def exception = thrown(BusinessException)
        exception.errorCode == PaymentLinkSendErrorEnum.EMAIL_CHECK_DATA_ERROR.getErrorCode()
        exception.errorMessage == "Validation error"
    }

    def "test checkData with multiple check results - all successful"() {
        given: "Create checkDataResponseType with multiple successful check results"
        def result1 = new DataCheckResult()
        result1.setCheckResult(BooleanUtil.parseStr(true))
        result1.setFriendlyMessage("Success 1")

        def result2 = new DataCheckResult()
        result2.setCheckResult(BooleanUtil.parseStr(true))
        result2.setFriendlyMessage("Success 2")

        def checkDataResponse = new CheckDataResponseType()
        checkDataResponse.setResultList([result1, result2])

        when: "Call checkData"
        PaymentLinkSendUtil.checkData(checkDataResponse)

        then: "Should not throw exception"
        noExceptionThrown()
    }

    def "test WAITPAY constant value"() {
        expect: "WAITPAY constant should have correct value"
        PaymentLinkSendUtil.WAITPAY == "WaitPay"
    }
}