package com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.mapperofgetorderindex

import com.ctrip.corp.bff.framework.template.entity.MapString
import com.ctrip.corp.bff.framework.template.entity.PosEnum
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderIndexGetRequestType
import spock.lang.*

class MapperOfOrderIndexGetRequestTypeTest extends Specification {

    MapperOfOrderIndexGetRequestType mapper = new MapperOfOrderIndexGetRequestType()

    def "test convert with valid input"() {
        given: "A valid Tuple2 with TemplateSoaRequestType and orderId"
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl",
            "userAgent", "deviceType", "language", "requestId", SourceFrom.H5,
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc",
            PosEnum.CHINA, "timezoneOffsetMinutesNew",
            [new MapString("key", "value")], [new MapString("key", "value")],
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE,
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )
        def orderId = "12345"
        def input = Tuple2.of(templateSoaRequestType, orderId)

        when: "Converting the input"
        OrderIndexGetRequestType result = mapper.convert(input)

        then: "The result should be properly mapped"
        result != null
        result.getOrderId() == 12345L
        result.getOperationChannel() == "App"
        result.getDeleteFlag() == true
    }

    def "test convert with null input should throw exception"() {
        when: "Converting null input"
        mapper.convert(null)

        then: "Should throw NullPointerException"
        thrown(NullPointerException)
    }

    def "test convert with null orderId"() {
        given: "A Tuple2 with valid TemplateSoaRequestType but null orderId"
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl",
            "userAgent", "deviceType", "language", "requestId", SourceFrom.Native,
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc",
            PosEnum.CHINA, "timezoneOffsetMinutesNew",
            [new MapString("key", "value")], [new MapString("key", "value")],
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE,
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )
        def input = Tuple2.of(templateSoaRequestType, null)

        when: "Converting the input"
        OrderIndexGetRequestType result = mapper.convert(input)

        then: "The result should handle null orderId gracefully"
        result != null
        result.getOrderId() == null || result.getOrderId() == 0L
        result.getOperationChannel() == "App"
        result.getDeleteFlag() == true
    }

    def "test check method always returns null"() {
        given: "Any input"
        def input = Tuple2.of(Mock(TemplateSoaRequestType), "12345")

        when: "Calling check method"
        ParamCheckResult result = mapper.check(input)

        then: "Should always return null"
        result == null
    }

    @Unroll
    def "test buildOperationChannel for SourceFrom #sourceFrom returns #expectedChannel"() {
        given: "A TemplateSoaRequestType with specific SourceFrom"
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl",
            "userAgent", "deviceType", "language", "requestId", sourceFrom,
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc",
            PosEnum.CHINA, "timezoneOffsetMinutesNew",
            [new MapString("key", "value")], [new MapString("key", "value")],
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE,
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )

        when: "Building operation channel"
        String result = mapper.buildOperationChannel(templateSoaRequestType)

        then: "Should return expected channel"
        result == expectedChannel

        where:
        sourceFrom          | expectedChannel
        SourceFrom.H5       | "App"
        SourceFrom.Native   | "App"
        SourceFrom.CRN      | "App"
        SourceFrom.Offline  | "Offline"
    }

    def "test buildOperationChannel for default case"() {
        given: "A TemplateSoaRequestType with a SourceFrom that falls to default"
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl",
            "userAgent", "deviceType", "language", "requestId", SourceFrom.Online, // Online falls to default
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc",
            PosEnum.CHINA, "timezoneOffsetMinutesNew",
            [new MapString("key", "value")], [new MapString("key", "value")],
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE,
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )

        when: "Building operation channel"
        String result = mapper.buildOperationChannel(templateSoaRequestType)

        then: "Should return Online for default case"
        result == "Online"
    }

    def "test map method integration"() {
        given: "A valid Tuple2 input"
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl",
            "userAgent", "deviceType", "language", "requestId", SourceFrom.CRN,
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc",
            PosEnum.CHINA, "timezoneOffsetMinutesNew",
            [new MapString("key", "value")], [new MapString("key", "value")],
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE,
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )
        def orderId = "67890"
        def input = Tuple2.of(templateSoaRequestType, orderId)

        when: "Calling map method"
        OrderIndexGetRequestType result = mapper.map(input)

        then: "Should return properly mapped result"
        result != null
        result.getOrderId() == 67890L
        result.getOperationChannel() == "App"
        result.getDeleteFlag() == true
    }

    def "test convert with invalid orderId format"() {
        given: "A Tuple2 with invalid orderId format"
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl",
            "userAgent", "deviceType", "language", "requestId", SourceFrom.Offline,
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc",
            PosEnum.CHINA, "timezoneOffsetMinutesNew",
            [new MapString("key", "value")], [new MapString("key", "value")],
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE,
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )
        def orderId = "invalid_number"
        def input = Tuple2.of(templateSoaRequestType, orderId)

        when: "Converting the input with invalid orderId"
        OrderIndexGetRequestType result = mapper.convert(input)

        then: "Should handle invalid orderId gracefully or throw appropriate exception"
        result != null
        result.getOperationChannel() == "Offline"
        result.getDeleteFlag() == true
    }
}