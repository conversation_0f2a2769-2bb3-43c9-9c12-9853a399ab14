package com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.corpbfftoolsservice

import spock.lang.Specification

class HandlerOfCheckDataTest extends Specification {

    def handlerOfCheckData = new HandlerOfCheckData()

    def setup() {
    }

    def "test getMethodName should return correct method name"() {
        when: "调用getMethodName方法"
        String result = handlerOfCheckData.getMethodName()

        then: "应该返回正确的方法名"
        result == "checkData"
    }
}