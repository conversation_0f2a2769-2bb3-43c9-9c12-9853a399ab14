package com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.ordermessagecentersenderserviceclient

import spock.lang.Specification


class HandlerOfManualSendMessageTest  extends Specification {
        def handlerOfManualSendMessage = new HandlerOfManualSendMessage()

        def setup() {
        }

        def "test getMethodName should return correct method name"() {
            when: "调用getMethodName方法"
            String result = handlerOfManualSendMessage.getMethodName()

            then: "应该返回正确的方法名"
            result == "manualSendMessage"
        }
}