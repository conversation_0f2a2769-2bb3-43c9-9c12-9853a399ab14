package com.ctrip.corp.bff.im.trip.processor

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.entity.MapString
import com.ctrip.corp.bff.framework.template.entity.PosEnum
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.contract.vo.EmailInfoVO
import com.ctrip.corp.bff.framework.template.handler.WaitFuture
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.corp.bff.im.trip.common.util.PaymentLinkSendUtil
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.corpbfftoolsservice.HandlerOfCheckData
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.handleroforderindexserviceclient.HandlerOfGetOrderIndex
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.mapperofgetorderindex.MapperOfOrderIndexGetRequestType
import com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.ordermessagecentersenderserviceclient.HandlerOfManualSendMessage
import com.ctrip.corp.bff.im.trip.mapper.mapperofpaymentlinksend.MapperOfCheckDataRequestType
import com.ctrip.corp.bff.im.trip.mapper.mapperofpaymentlinksend.MapperOfManualManualSendMessageRequestType
import com.ctrip.corp.bff.im.trip.mapper.mapperofpaymentlinksend.MapperOfPaymentLinkSendResponseVO
import com.ctrip.corp.bff.mcinfo.trip.contract.PaymentLinkSendRequestVO
import com.ctrip.corp.bff.mcinfo.trip.contract.PaymentLinkSendResponseVO
import com.ctrip.corp.bff.tools.contract.CheckDataRequestType
import com.ctrip.corp.bff.tools.contract.CheckDataResponseType
import com.ctrip.corp.order.messagecenter.sender.contract.ManualSendMessageRequestType
import com.ctrip.corp.order.messagecenter.sender.contract.ManualSendMessageResponseType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderBasicInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderDetailInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderIndexGetRequestType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderIndexGetResponseType
import mockit.Mock
import mockit.MockUp
import spock.lang.*


class ProcessorOfPaymentLinkSendTest extends Specification {
    
    ProcessorOfPaymentLinkSend processor = new ProcessorOfPaymentLinkSend()
    
    // Mock dependencies
    HandlerOfGetOrderIndex mockHandlerOfGetOrderIndex = Mock()
    MapperOfOrderIndexGetRequestType mockMapperOfOrderIndexGetRequestType = Mock()
    MapperOfPaymentLinkSendResponseVO mockMapperOfPaymentLinkSendResponseVO = Mock()
    HandlerOfManualSendMessage mockHandlerOfManualSendMessage = Mock()
    MapperOfManualManualSendMessageRequestType mockMapperOfManualManualSendMessageRequestType = Mock()
    HandlerOfCheckData mockHandlerOfCheckData = Mock()
    MapperOfCheckDataRequestType mockMapperOfCheckDataRequestType = Mock()

    def setup() {
        processor.handlerOfGetOrderIndex = mockHandlerOfGetOrderIndex
        processor.mapperOfOrderIndexGetRequestType = mockMapperOfOrderIndexGetRequestType
        processor.mapperOfPaymentLinkSendResponseVO = mockMapperOfPaymentLinkSendResponseVO
        processor.handlerOfManualSendMessage = mockHandlerOfManualSendMessage
        processor.mapperOfManualManualSendMessageRequestType = mockMapperOfManualManualSendMessageRequestType
        processor.handlerOfCheckData = mockHandlerOfCheckData
        processor.mapperOfCheckDataRequestType = mockMapperOfCheckDataRequestType
    }

    def "test execute with WaitPay order status - should send manual message"() {
        given: "A valid PaymentLinkSendRequestVO"
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl", 
            "userAgent", "deviceType", "language", "requestId", SourceFrom.H5, 
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc", 
            PosEnum.CHINA, "timezoneOffsetMinutesNew", 
            [new MapString("key", "value")], [new MapString("key", "value")], 
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE, 
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )
        
        def emailInfo = new EmailInfoVO()
        emailInfo.setTransferEmail("<EMAIL>")
        emailInfo.setEmail("<EMAIL>")
        
        def request = new PaymentLinkSendRequestVO()
        request.setRequestHeader(templateSoaRequestType)
        request.setEmailInfo(emailInfo)
        request.setOrderId("12345")
        request.setPaymentMethodCode("BOOKING_USER")
        request.setSendEmailLanguage("en")

        def checkDataRequestType = Mock(CheckDataRequestType)
        def checkDataResponseType = Mock(CheckDataResponseType)
        def checkDataWaitFuture = Mock(WaitFuture) {
            getWithoutError() >> checkDataResponseType
        }
        
        def orderIndexGetRequestType = Mock(OrderIndexGetRequestType)
        def orderBasicInfo = new OrderBasicInfoType()
        orderBasicInfo.setOrderStatus("WaitPay")
        def orderDetailInfo = new OrderDetailInfoType()
        orderDetailInfo.setBasicInfo(orderBasicInfo)
        def orderIndexGetResponseType = new OrderIndexGetResponseType()
        orderIndexGetResponseType.setOrderDetailInfo(orderDetailInfo)
        def orderIndexWaitFuture = Mock(WaitFuture) {
            getWithoutError() >> orderIndexGetResponseType
        }
        
        def manualSendMessageRequestType = Mock(ManualSendMessageRequestType)
        def manualSendMessageResponseType = Mock(ManualSendMessageResponseType)
        def manualSendWaitFuture = Mock(WaitFuture) {
            getWithoutError() >> manualSendMessageResponseType
        }
        
        def expectedResponse = Mock(PaymentLinkSendResponseVO)

        new MockUp<PaymentLinkSendUtil>() {
            @Mock
            void checkData(CheckDataResponseType response) {
                // Do nothing - successful check
            }
            
            @Mock
            boolean needManualSendMessage(OrderIndexGetResponseType response) {
                return true  // WaitPay status
            }
        }

        when: "Executing the processor"
        PaymentLinkSendResponseVO result = processor.execute(request)

        then: "All dependencies should be called correctly"
        1 * mockMapperOfCheckDataRequestType.map(_ as Tuple1) >> checkDataRequestType
        1 * mockHandlerOfCheckData.handleAsync(checkDataRequestType) >> checkDataWaitFuture
        1 * mockMapperOfOrderIndexGetRequestType.map(_ as Tuple2) >> orderIndexGetRequestType
        1 * mockHandlerOfGetOrderIndex.handleAsync(orderIndexGetRequestType) >> orderIndexWaitFuture
        1 * mockMapperOfManualManualSendMessageRequestType.map(_ as Tuple2) >> manualSendMessageRequestType
        1 * mockHandlerOfManualSendMessage.handleAsync(manualSendMessageRequestType) >> manualSendWaitFuture
        1 * mockMapperOfPaymentLinkSendResponseVO.map(_ as Tuple2) >> expectedResponse
        
        and: "Should return the expected response"
        result == expectedResponse
    }

    def "test execute with non-WaitPay order status - should not send manual message"() {
        given: "A valid PaymentLinkSendRequestVO"
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl", 
            "userAgent", "deviceType", "language", "requestId", SourceFrom.H5, 
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc", 
            PosEnum.CHINA, "timezoneOffsetMinutesNew", 
            [new MapString("key", "value")], [new MapString("key", "value")], 
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE, 
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )
        
        def emailInfo = new EmailInfoVO()
        emailInfo.setTransferEmail("<EMAIL>")
        
        def request = new PaymentLinkSendRequestVO()
        request.setRequestHeader(templateSoaRequestType)
        request.setEmailInfo(emailInfo)
        request.setOrderId("12345")
        request.setPaymentMethodCode("BOOKING_USER")
        request.setSendEmailLanguage("en")

        def checkDataRequestType = Mock(CheckDataRequestType)
        def checkDataResponseType = Mock(CheckDataResponseType)
        def checkDataWaitFuture = Mock(WaitFuture) {
            getWithoutError() >> checkDataResponseType
        }
        
        def orderIndexGetRequestType = Mock(OrderIndexGetRequestType)
        def orderBasicInfo = new OrderBasicInfoType()
        orderBasicInfo.setOrderStatus("Paid")
        def orderDetailInfo = new OrderDetailInfoType()
        orderDetailInfo.setBasicInfo(orderBasicInfo)
        def orderIndexGetResponseType = new OrderIndexGetResponseType()
        orderIndexGetResponseType.setOrderDetailInfo(orderDetailInfo)
        def orderIndexWaitFuture = Mock(WaitFuture) {
            getWithoutError() >> orderIndexGetResponseType
        }
        
        def expectedResponse = Mock(PaymentLinkSendResponseVO)

        new MockUp<PaymentLinkSendUtil>() {
            @Mock
            void checkData(CheckDataResponseType response) {
                // Do nothing - successful check
            }
            
            @Mock
            boolean needManualSendMessage(OrderIndexGetResponseType response) {
                return false  // Non-WaitPay status
            }
        }

        when: "Executing the processor"
        PaymentLinkSendResponseVO result = processor.execute(request)

        then: "Check data and order index should be called, but not manual send"
        1 * mockMapperOfCheckDataRequestType.map(_ as Tuple1) >> checkDataRequestType
        1 * mockHandlerOfCheckData.handleAsync(checkDataRequestType) >> checkDataWaitFuture
        1 * mockMapperOfOrderIndexGetRequestType.map(_ as Tuple2) >> orderIndexGetRequestType
        1 * mockHandlerOfGetOrderIndex.handleAsync(orderIndexGetRequestType) >> orderIndexWaitFuture
        0 * mockMapperOfManualManualSendMessageRequestType.map(_ as Tuple2)
        0 * mockHandlerOfManualSendMessage.handleAsync(_ as ManualSendMessageRequestType)
        1 * mockMapperOfPaymentLinkSendResponseVO.map(_ as Tuple2) >> expectedResponse
        
        and: "Should return the expected response"
        result == expectedResponse
    }


    def "test tracking method returns null"() {
        given: "Valid request and response objects"
        def request = Mock(PaymentLinkSendRequestVO)
        def response = Mock(PaymentLinkSendResponseVO)

        when: "Calling tracking method"
        Map<String, String> result = processor.tracking(request, response)

        then: "Should return null"
        result == null
    }
}
