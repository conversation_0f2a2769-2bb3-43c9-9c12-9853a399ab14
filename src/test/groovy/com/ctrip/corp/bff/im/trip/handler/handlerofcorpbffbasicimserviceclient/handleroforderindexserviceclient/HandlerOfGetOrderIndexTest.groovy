package com.ctrip.corp.bff.im.trip.handler.handlerofcorpbffbasicimserviceclient.handleroforderindexserviceclient

import spock.lang.Specification

class HandlerOfGetOrderIndexTest  extends Specification {
   def handlerOfGetOrderIndex = new HandlerOfGetOrderIndex()

       def setup() {
       }

       def "test getMethodName should return correct method name"() {
           when: "调用getMethodName方法"
           String result = handlerOfGetOrderIndex.getMethodName()

           then: "应该返回正确的方法名"
           result == "getOrderIndex"
       }
   }