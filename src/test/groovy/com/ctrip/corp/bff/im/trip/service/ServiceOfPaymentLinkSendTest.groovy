package com.ctrip.corp.bff.im.trip.service

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.entity.MapString
import com.ctrip.corp.bff.framework.template.entity.PosEnum
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.contract.vo.EmailInfoVO
import com.ctrip.corp.bff.framework.template.processor.Processor
import com.ctrip.corp.bff.im.trip.common.enums.error.PaymentLinkSendErrorEnum
import com.ctrip.corp.bff.im.trip.processor.ProcessorOfPaymentLinkSend
import com.ctrip.corp.bff.mcinfo.trip.contract.PaymentLinkSendRequestVO
import com.ctrip.corp.bff.mcinfo.trip.contract.PaymentLinkSendResponseVO
import spock.lang.*


class ServiceOfPaymentLinkSendTest extends Specification {

    ServiceOfPaymentLinkSend service = new ServiceOfPaymentLinkSend()
    ProcessorOfPaymentLinkSend mockProcessor = Mock()

    def setup() {
        service.processor = mockProcessor
    }

    def "test validateRequest with valid request should pass"() {
        given: "A valid PaymentLinkSendRequestVO"
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl",
            "userAgent", "deviceType", "language", "requestId", SourceFrom.H5,
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc",
            PosEnum.CHINA, "timezoneOffsetMinutesNew",
            [new MapString("key", "value")], [new MapString("key", "value")],
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE,
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )

        def emailInfo = new EmailInfoVO()
        emailInfo.setTransferEmail("<EMAIL>")
        emailInfo.setEmail("<EMAIL>")

        def request = new PaymentLinkSendRequestVO()
        request.setRequestHeader(templateSoaRequestType)
        request.setEmailInfo(emailInfo)
        request.setOrderId("12345")
        request.setPaymentMethodCode("BOOKING_USER")
        request.setSendEmailLanguage("en")

        when: "Validating the request"
        service.validateRequest(request)

        then: "Should not throw any exception"
        noExceptionThrown()
    }

    def "test getProcessor returns injected processor"() {
        given: "A valid PaymentLinkSendRequestVO"
        def request = Mock(PaymentLinkSendRequestVO)

        when: "Getting the processor"
        def result = service.getProcessor(request)

        then: "Should return the injected processor"
        result == mockProcessor
    }
}