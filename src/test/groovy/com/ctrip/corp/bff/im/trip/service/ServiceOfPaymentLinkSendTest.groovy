package com.ctrip.corp.bff.im.trip.service

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.entity.MapString
import com.ctrip.corp.bff.framework.template.entity.PosEnum
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.contract.vo.EmailInfoVO
import com.ctrip.corp.bff.framework.template.processor.Processor
import com.ctrip.corp.bff.im.trip.common.enums.error.PaymentLinkSendErrorEnum
import com.ctrip.corp.bff.im.trip.processor.ProcessorOfPaymentLinkSend
import com.ctrip.corp.bff.mcinfo.trip.contract.PaymentLinkSendRequestVO
import com.ctrip.corp.bff.mcinfo.trip.contract.PaymentLinkSendResponseVO
import spock.lang.*


class ServiceOfPaymentLinkSendTest extends Specification {

    ServiceOfPaymentLinkSend service = new ServiceOfPaymentLinkSend()
    ProcessorOfPaymentLinkSend mockProcessor = Mock()

    def setup() {
        service.processor = mockProcessor
    }

    private TemplateSoaRequestType createValidTemplateSoaRequestType() {
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", "eid", "accountPos")
        return new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl",
            "userAgent", "deviceType", "language", "requestId", SourceFrom.H5,
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc",
            PosEnum.CHINA, "timezoneOffsetMinutesNew",
            [new MapString("key", "value")], [new MapString("key", "value")],
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE,
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )
    }

    // Helper method to create a valid EmailInfoVO
    private EmailInfoVO createValidEmailInfo() {
        def emailInfo = new EmailInfoVO()
        emailInfo.setTransferEmail("<EMAIL>")
        emailInfo.setEmail("<EMAIL>")
        return emailInfo
    }

    // Helper method to create a valid PaymentLinkSendRequestVO
    private PaymentLinkSendRequestVO createValidRequest() {
        def request = new PaymentLinkSendRequestVO()
        request.setRequestHeader(createValidTemplateSoaRequestType())
        request.setEmailInfo(createValidEmailInfo())
        request.setOrderId("12345")
        request.setPaymentMethodCode("BOOKING_USER")
        request.setSendEmailLanguage("en")
        return request
    }

    def "test validateRequest with valid request should pass"() {
        given: "A valid PaymentLinkSendRequestVO"
        def request = createValidRequest()

        when: "Validating the request"
        service.validateRequest(request)

        then: "Should not throw any exception"
        noExceptionThrown()
    }

    @Unroll
    def "test validateRequest should throw BusinessException when orderId is #description"() {
        given: "A request with invalid orderId"
        def request = createValidRequest()
        request.setOrderId(orderId)

        when: "Validating the request"
        service.validateRequest(request)

        then: "Should throw BusinessException with REQUEST_PARAM_CHECK_ERROR"
        def exception = thrown(BusinessException)
        exception.errorCode == PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR.getErrorCode()
        exception.errorMessage == PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR.getErrorMessage()

        where:
        orderId | description
        null    | "null"
        ""      | "empty string"
        "   "   | "blank string"
    }

    @Unroll
    def "test validateRequest should throw BusinessException when userId is #description"() {
        given: "A request with invalid userId in header"
        def request = createValidRequest()
        def templateHeader = new TemplateHeader(userId, "corpId", "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl",
            "userAgent", "deviceType", "language", "requestId", SourceFrom.H5,
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc",
            PosEnum.CHINA, "timezoneOffsetMinutesNew",
            [new MapString("key", "value")], [new MapString("key", "value")],
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE,
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )
        request.setRequestHeader(templateSoaRequestType)

        when: "Validating the request"
        service.validateRequest(request)

        then: "Should throw BusinessException with REQUEST_PARAM_CHECK_ERROR"
        def exception = thrown(BusinessException)
        exception.errorCode == PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR.getErrorCode()
        exception.errorMessage == PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR.getErrorMessage()

        where:
        userId | description
        null   | "null"
        ""     | "empty string"
        "   "  | "blank string"
    }

    @Unroll
    def "test validateRequest should throw BusinessException when corpId is #description"() {
        given: "A request with invalid corpId in header"
        def request = createValidRequest()
        def templateHeader = new TemplateHeader("userId", corpId, "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl",
            "userAgent", "deviceType", "language", "requestId", SourceFrom.H5,
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc",
            PosEnum.CHINA, "timezoneOffsetMinutesNew",
            [new MapString("key", "value")], [new MapString("key", "value")],
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE,
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )
        request.setRequestHeader(templateSoaRequestType)

        when: "Validating the request"
        service.validateRequest(request)

        then: "Should throw BusinessException with REQUEST_PARAM_CHECK_ERROR"
        def exception = thrown(BusinessException)
        exception.errorCode == PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR.getErrorCode()
        exception.errorMessage == PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR.getErrorMessage()

        where:
        corpId | description
        null   | "null"
        ""     | "empty string"
        "   "  | "blank string"
    }

    @Unroll
    def "test validateRequest should throw BusinessException when eid is #description"() {
        given: "A request with invalid eid in header"
        def request = createValidRequest()
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", eid, "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl",
            "userAgent", "deviceType", "language", "requestId", SourceFrom.H5,
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc",
            PosEnum.CHINA, "timezoneOffsetMinutesNew",
            [new MapString("key", "value")], [new MapString("key", "value")],
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE,
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )
        request.setRequestHeader(templateSoaRequestType)

        when: "Validating the request"
        service.validateRequest(request)

        then: "Should throw BusinessException with REQUEST_PARAM_CHECK_ERROR"
        def exception = thrown(BusinessException)
        exception.errorCode == PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR.getErrorCode()
        exception.errorMessage == PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR.getErrorMessage()

        where:
        eid    | description
        null   | "null"
        ""     | "empty string"
        "   "  | "blank string"
    }

    def "test validateRequest should throw BusinessException when requestHeader is null"() {
        given: "A request with null requestHeader"
        def request = createValidRequest()
        request.setRequestHeader(null)

        when: "Validating the request"
        service.validateRequest(request)

        then: "Should throw BusinessException with REQUEST_PARAM_CHECK_ERROR"
        def exception = thrown(BusinessException)
        exception.errorCode == PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR.getErrorCode()
        exception.errorMessage == PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR.getErrorMessage()
    }

    def "test validateRequest should throw BusinessException when header is null"() {
        given: "A request with null header in requestHeader"
        def request = createValidRequest()
        def templateSoaRequestType = new TemplateSoaRequestType(
            null, "site", "sToken", "token", "cid", "clientIp", "rawUrl",
            "userAgent", "deviceType", "language", "requestId", SourceFrom.H5,
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc",
            PosEnum.CHINA, "timezoneOffsetMinutesNew",
            [new MapString("key", "value")], [new MapString("key", "value")],
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE,
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )
        request.setRequestHeader(templateSoaRequestType)

        when: "Validating the request"
        service.validateRequest(request)

        then: "Should throw BusinessException with REQUEST_PARAM_CHECK_ERROR"
        def exception = thrown(BusinessException)
        exception.errorCode == PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR.getErrorCode()
        exception.errorMessage == PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR.getErrorMessage()
    }

    @Unroll
    def "test validateRequest should throw BusinessException when transferEmail is #description"() {
        given: "A request with invalid transferEmail"
        def request = createValidRequest()
        def emailInfo = new EmailInfoVO()
        emailInfo.setTransferEmail(transferEmail)
        emailInfo.setEmail("<EMAIL>")
        request.setEmailInfo(emailInfo)

        when: "Validating the request"
        service.validateRequest(request)

        then: "Should throw BusinessException with MISS_EMAIL"
        def exception = thrown(BusinessException)
        exception.errorCode == PaymentLinkSendErrorEnum.MISS_EMAIL.getErrorCode()
        exception.errorMessage == PaymentLinkSendErrorEnum.MISS_EMAIL.getErrorMessage()

        where:
        transferEmail | description
        null          | "null"
        ""            | "empty string"
        "   "         | "blank string"
    }

    def "test validateRequest should throw BusinessException when emailInfo is null"() {
        given: "A request with null emailInfo"
        def request = createValidRequest()
        request.setEmailInfo(null)

        when: "Validating the request"
        service.validateRequest(request)

        then: "Should throw BusinessException with MISS_EMAIL"
        def exception = thrown(BusinessException)
        exception.errorCode == PaymentLinkSendErrorEnum.MISS_EMAIL.getErrorCode()
        exception.errorMessage == PaymentLinkSendErrorEnum.MISS_EMAIL.getErrorMessage()
    }

    def "test getProcessor returns injected processor"() {
        given: "A valid PaymentLinkSendRequestVO"
        def request = Mock(PaymentLinkSendRequestVO)

        when: "Getting the processor"
        def result = service.getProcessor(request)

        then: "Should return the injected processor"
        result == mockProcessor
    }
}