package com.ctrip.corp.bff.im.trip.mapper.mapperofpaymentlinksend

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.entity.MapString
import com.ctrip.corp.bff.framework.template.entity.PosEnum
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.contract.vo.EmailInfoVO
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.corp.bff.im.trip.common.enums.error.PaymentLinkSendErrorEnum
import com.ctrip.corp.bff.mcinfo.trip.contract.PaymentLinkSendRequestVO
import com.ctrip.corp.order.messagecenter.sender.contract.ManualSendMessageRequestType
import com.ctrip.corp.order.messagecenter.sender.contract.PairType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderBasicInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderDetailInfoType
import com.ctrip.soa.corp.order.orderindexservice.v1.OrderIndexGetResponseType
import spock.lang.*

class MapperOfManualManualSendMessageRequestTypeTest extends Specification {
    
    MapperOfManualManualSendMessageRequestType mapper = new MapperOfManualManualSendMessageRequestType()

    def "test convert with valid input for BOOKING_USER payment method"() {
        given: "A valid input with BOOKING_USER payment method"
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl", 
            "userAgent", "deviceType", "language", "requestId", SourceFrom.H5, 
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc", 
            PosEnum.CHINA, "timezoneOffsetMinutesNew", 
            [new MapString("key", "value")], [new MapString("key", "value")], 
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE, 
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )
        
        def emailInfo = new EmailInfoVO()
        emailInfo.setTransferEmail("<EMAIL>")
        emailInfo.setEmail("<EMAIL>")
        
        def paymentLinkSendRequestVO = new PaymentLinkSendRequestVO()
        paymentLinkSendRequestVO.setRequestHeader(templateSoaRequestType)
        paymentLinkSendRequestVO.setEmailInfo(emailInfo)
        paymentLinkSendRequestVO.setOrderId("12345")
        paymentLinkSendRequestVO.setPaymentMethodCode("BOOKING_USER")
        paymentLinkSendRequestVO.setSendEmailLanguage("en")
        
        def orderBasicInfo = new OrderBasicInfoType()
        orderBasicInfo.setProductLine(1)
        
        def orderDetailInfo = new OrderDetailInfoType()
        orderDetailInfo.setBasicInfo(orderBasicInfo)
        
        def orderIndexGetResponseType = new OrderIndexGetResponseType()
        orderIndexGetResponseType.setOrderDetailInfo(orderDetailInfo)
        
        def input = Tuple2.of(orderIndexGetResponseType, paymentLinkSendRequestVO)

        when: "Converting the input"
        ManualSendMessageRequestType result = mapper.convert(input)

        then: "The result should be properly mapped"
        result != null
        result.getRequestId() == "requestId"
        result.getEid() == "eid"
        result.getCorpID() == "corpId"
        result.getLanguage() == "language"
        result.getOrderID() == 12345L
        result.getUserID() == "userId"
        result.getScenarioCode() == "Payment_Notify_Manual"
        result.getProductLine() == 1
        
        def pairList = result.getPairList()
        pairList.size() == 3
        pairList.find { it.getKey() == "messageType" }.getValue() == "EMAIL"
        pairList.find { it.getKey() == "target" }.getValue() == "<EMAIL>"
        pairList.find { it.getKey() == "language" }.getValue() == "en"
    }

    def "test convert with valid input for OTHER_USER payment method"() {
        given: "A valid input with OTHER_USER payment method"
        def templateHeader = new TemplateHeader("userId2", "corpId2", "groupId2", "eid2", "accountPos2")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl", 
            "userAgent", "deviceType", "language2", "requestId2", SourceFrom.Native, 
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc", 
            PosEnum.CHINA, "timezoneOffsetMinutesNew", 
            [new MapString("key", "value")], [new MapString("key", "value")], 
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE, 
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )
        
        def emailInfo = new EmailInfoVO()
        emailInfo.setTransferEmail("<EMAIL>")
        emailInfo.setEmail("<EMAIL>")
        
        def paymentLinkSendRequestVO = new PaymentLinkSendRequestVO()
        paymentLinkSendRequestVO.setRequestHeader(templateSoaRequestType)
        paymentLinkSendRequestVO.setEmailInfo(emailInfo)
        paymentLinkSendRequestVO.setOrderId("67890")
        paymentLinkSendRequestVO.setPaymentMethodCode("OTHER_USER")
        paymentLinkSendRequestVO.setSendEmailLanguage("zh")
        
        def orderBasicInfo = new OrderBasicInfoType()
        orderBasicInfo.setProductLine(2)
        
        def orderDetailInfo = new OrderDetailInfoType()
        orderDetailInfo.setBasicInfo(orderBasicInfo)
        
        def orderIndexGetResponseType = new OrderIndexGetResponseType()
        orderIndexGetResponseType.setOrderDetailInfo(orderDetailInfo)
        
        def input = Tuple2.of(orderIndexGetResponseType, paymentLinkSendRequestVO)

        when: "Converting the input"
        ManualSendMessageRequestType result = mapper.convert(input)

        then: "The result should be properly mapped"
        result != null
        result.getRequestId() == "requestId2"
        result.getEid() == "eid2"
        result.getCorpID() == "corpId2"
        result.getLanguage() == "language2"
        result.getOrderID() == 67890L
        result.getUserID() == "userId2"
        result.getScenarioCode() == "Lieu_Payment_Notify_Manual"
        result.getProductLine() == 2
        
        def pairList = result.getPairList()
        pairList.size() == 3
        pairList.find { it.getKey() == "messageType" }.getValue() == "EMAIL"
        pairList.find { it.getKey() == "target" }.getValue() == "<EMAIL>"
        pairList.find { it.getKey() == "language" }.getValue() == "zh"
    }

    def "test convert with unknown payment method code"() {
        given: "A valid input with unknown payment method"
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl", 
            "userAgent", "deviceType", "language", "requestId", SourceFrom.H5, 
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc", 
            PosEnum.CHINA, "timezoneOffsetMinutesNew", 
            [new MapString("key", "value")], [new MapString("key", "value")], 
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE, 
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )
        
        def emailInfo = new EmailInfoVO()
        emailInfo.setTransferEmail("<EMAIL>")
        
        def paymentLinkSendRequestVO = new PaymentLinkSendRequestVO()
        paymentLinkSendRequestVO.setRequestHeader(templateSoaRequestType)
        paymentLinkSendRequestVO.setEmailInfo(emailInfo)
        paymentLinkSendRequestVO.setOrderId("12345")
        paymentLinkSendRequestVO.setPaymentMethodCode("UNKNOWN_METHOD")
        paymentLinkSendRequestVO.setSendEmailLanguage("en")
        
        def orderIndexGetResponseType = new OrderIndexGetResponseType()
        def input = Tuple2.of(orderIndexGetResponseType, paymentLinkSendRequestVO)

        when: "Converting the input"
        ManualSendMessageRequestType result = mapper.convert(input)

        then: "The result should have null scenario code"
        result != null
        result.getScenarioCode() == null
    }

    def "test convert with null input should throw exception"() {
        when: "Converting null input"
        mapper.convert(null)

        then: "Should throw NullPointerException"
        thrown(NullPointerException)
    }

    def "test convert with null PaymentLinkSendRequestVO should throw exception"() {
        given: "A Tuple2 with null PaymentLinkSendRequestVO"
        def orderIndexGetResponseType = new OrderIndexGetResponseType()
        def input = Tuple2.of(orderIndexGetResponseType, null)

        when: "Converting the input"
        mapper.convert(input)

        then: "Should throw NullPointerException"
        thrown(NullPointerException)
    }

    def "test convert with null orderIndexGetResponseType"() {
        given: "A Tuple2 with null orderIndexGetResponseType"
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl", 
            "userAgent", "deviceType", "language", "requestId", SourceFrom.H5, 
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc", 
            PosEnum.CHINA, "timezoneOffsetMinutesNew", 
            [new MapString("key", "value")], [new MapString("key", "value")], 
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE, 
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )
        
        def emailInfo = new EmailInfoVO()
        emailInfo.setTransferEmail("<EMAIL>")
        
        def paymentLinkSendRequestVO = new PaymentLinkSendRequestVO()
        paymentLinkSendRequestVO.setRequestHeader(templateSoaRequestType)
        paymentLinkSendRequestVO.setEmailInfo(emailInfo)
        paymentLinkSendRequestVO.setOrderId("12345")
        paymentLinkSendRequestVO.setPaymentMethodCode("BOOKING_USER")
        paymentLinkSendRequestVO.setSendEmailLanguage("en")
        
        def input = Tuple2.of(null, paymentLinkSendRequestVO)

        when: "Converting the input"
        ManualSendMessageRequestType result = mapper.convert(input)

        then: "The result should handle null orderIndexGetResponseType gracefully"
        result != null
        result.getProductLine() == null
    }

    def "test check method with valid productLine should return null"() {
        given: "A valid input with productLine set to 0 (which should pass check)"
        def orderBasicInfo = new OrderBasicInfoType()
        orderBasicInfo.setProductLine(0)

        def orderDetailInfo = new OrderDetailInfoType()
        orderDetailInfo.setBasicInfo(orderBasicInfo)

        def orderIndexGetResponseType = new OrderIndexGetResponseType()
        orderIndexGetResponseType.setOrderDetailInfo(orderDetailInfo)

        def paymentLinkSendRequestVO = Mock(PaymentLinkSendRequestVO)
        def input = Tuple2.of(orderIndexGetResponseType, paymentLinkSendRequestVO)

        when: "Calling check method"
        ParamCheckResult result = mapper.check(input)

        then: "Should return null"
        result == null
    }


    def "test check method with null orderIndexGetResponseType should return null"() {
        given: "A Tuple2 with null orderIndexGetResponseType"
        def paymentLinkSendRequestVO = Mock(PaymentLinkSendRequestVO)
        def input = Tuple2.of(null, paymentLinkSendRequestVO)

        when: "Calling check method"
        ParamCheckResult result = mapper.check(input)

        then: "Should return null"
        result == null
    }

    @Unroll
    def "test buildScenarioCode for payment method #paymentMethodCode returns #expectedScenario"() {
        given: "A PaymentLinkSendRequestVO with specific payment method code"
        def paymentLinkSendRequestVO = Mock(PaymentLinkSendRequestVO) {
            getPaymentMethodCode() >> paymentMethodCode
        }
        def orderIndexGetResponseType = Mock(OrderIndexGetResponseType)

        when: "Building scenario code"
        String result = mapper.buildScenarioCode(orderIndexGetResponseType, paymentLinkSendRequestVO)

        then: "Should return expected scenario"
        result == expectedScenario

        where:
        paymentMethodCode | expectedScenario
        "BOOKING_USER"    | "Payment_Notify_Manual"
        "booking_user"    | "Payment_Notify_Manual"
        "OTHER_USER"      | "Lieu_Payment_Notify_Manual"
        "other_user"      | "Lieu_Payment_Notify_Manual"
        "UNKNOWN_METHOD"  | null
        null              | null
        ""                | null
    }

    def "test map method integration"() {
        given: "A valid input for integration test"
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl",
            "userAgent", "deviceType", "language", "requestId", SourceFrom.CRN,
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc",
            PosEnum.CHINA, "timezoneOffsetMinutesNew",
            [new MapString("key", "value")], [new MapString("key", "value")],
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE,
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )

        def emailInfo = new EmailInfoVO()
        emailInfo.setTransferEmail("<EMAIL>")
        emailInfo.setEmail("<EMAIL>")

        def paymentLinkSendRequestVO = new PaymentLinkSendRequestVO()
        paymentLinkSendRequestVO.setRequestHeader(templateSoaRequestType)
        paymentLinkSendRequestVO.setEmailInfo(emailInfo)
        paymentLinkSendRequestVO.setOrderId("99999")
        paymentLinkSendRequestVO.setPaymentMethodCode("OTHER_USER")
        paymentLinkSendRequestVO.setSendEmailLanguage("fr")

        def orderBasicInfo = new OrderBasicInfoType()
        orderBasicInfo.setProductLine(0)

        def orderDetailInfo = new OrderDetailInfoType()
        orderDetailInfo.setBasicInfo(orderBasicInfo)

        def orderIndexGetResponseType = new OrderIndexGetResponseType()
        orderIndexGetResponseType.setOrderDetailInfo(orderDetailInfo)

        def input = Tuple2.of(orderIndexGetResponseType, paymentLinkSendRequestVO)

        when: "Calling map method"
        ManualSendMessageRequestType result = mapper.map(input)

        then: "Should return properly mapped result"
        result != null
        result.getRequestId() == "requestId"
        result.getEid() == "eid"
        result.getCorpID() == "corpId"
        result.getLanguage() == "language"
        result.getOrderID() == 99999L
        result.getUserID() == "userId"
        result.getScenarioCode() == "Lieu_Payment_Notify_Manual"
        result.getProductLine() == 0

        def pairList = result.getPairList()
        pairList.size() == 3
        pairList.find { it.getKey() == "messageType" }.getValue() == "EMAIL"
        pairList.find { it.getKey() == "target" }.getValue() == "<EMAIL>"
        pairList.find { it.getKey() == "language" }.getValue() == "fr"
    }

    def "test convert with null emailInfo should throw exception"() {
        given: "A PaymentLinkSendRequestVO with null emailInfo"
        def templateHeader = new TemplateHeader("userId", "corpId", "groupId", "eid", "accountPos")
        def templateSoaRequestType = new TemplateSoaRequestType(
            templateHeader, "site", "sToken", "token", "cid", "clientIp", "rawUrl",
            "userAgent", "deviceType", "language", "requestId", SourceFrom.H5,
            "transactionID", "vId", "channel", "gatewayHost", "gatewayIdc",
            PosEnum.CHINA, "timezoneOffsetMinutesNew",
            [new MapString("key", "value")], [new MapString("key", "value")],
            "subChannel", "customRequestRouter", "miceToken", "ticket", Boolean.TRUE,
            [new MapString("key", "value")], "bookModel", "serviceName", "operationName", "timeZoneId"
        )

        def paymentLinkSendRequestVO = new PaymentLinkSendRequestVO()
        paymentLinkSendRequestVO.setRequestHeader(templateSoaRequestType)
        paymentLinkSendRequestVO.setEmailInfo(null)
        paymentLinkSendRequestVO.setOrderId("12345")
        paymentLinkSendRequestVO.setPaymentMethodCode("BOOKING_USER")
        paymentLinkSendRequestVO.setSendEmailLanguage("en")

        def orderIndexGetResponseType = new OrderIndexGetResponseType()
        def input = Tuple2.of(orderIndexGetResponseType, paymentLinkSendRequestVO)

        when: "Converting the input"
        mapper.convert(input)

        then: "Should throw NullPointerException"
        thrown(NullPointerException)
    }
}
