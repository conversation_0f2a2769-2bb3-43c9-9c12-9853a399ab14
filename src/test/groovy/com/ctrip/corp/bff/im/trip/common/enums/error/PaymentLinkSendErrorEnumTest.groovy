package com.ctrip.corp.bff.im.trip.common.enums.error

import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import mockit.Mock
import mockit.MockUp
import spock.lang.Specification
import spock.lang.Unroll

class PaymentLinkSendErrorEnumTest extends Specification {

    def "test enum values exist"() {
        expect: "All enum values should be defined"
        PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR != null
        PaymentLinkSendErrorEnum.MANUAL_SEND_MESSAGE_ERROR != null
        PaymentLinkSendErrorEnum.PRODUCT_LINE_ERROR != null
        PaymentLinkSendErrorEnum.MISS_EMAIL != null
        PaymentLinkSendErrorEnum.EMAIL_CHECK_DATA_ERROR != null
    }

    @Unroll
    def "test getErrorCode for #enumValue"() {
        expect:
        enumValue.getErrorCode() == expectedErrorCode

        where:
        enumValue                                           | expectedErrorCode
        PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR  | 600
        PaymentLinkSendErrorEnum.MANUAL_SEND_MESSAGE_ERROR  | 601
        PaymentLinkSendErrorEnum.PRODUCT_LINE_ERROR         | 602
        PaymentLinkSendErrorEnum.MISS_EMAIL                 | 603
        PaymentLinkSendErrorEnum.EMAIL_CHECK_DATA_ERROR     | 604
    }

    @Unroll
    def "test getErrorMessage for #enumValue"() {
        expect:
        enumValue.getErrorMessage() == expectedErrorMessage

        where:
        enumValue                                           | expectedErrorMessage
        PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR  | "param error"
        PaymentLinkSendErrorEnum.MANUAL_SEND_MESSAGE_ERROR  | "manualSendMessage error"
        PaymentLinkSendErrorEnum.PRODUCT_LINE_ERROR         | "productLine error"
        PaymentLinkSendErrorEnum.MISS_EMAIL                 | "miss email"
        PaymentLinkSendErrorEnum.EMAIL_CHECK_DATA_ERROR     | "email error"
    }

    @Unroll
    def "test getSharkKey for #enumValue"() {
        expect:
        enumValue.getSharkKey() == expectedSharkKey

        where:
        enumValue                                           | expectedSharkKey
        PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR  | "trip.biz.bff.paymentlinksend.error.param"
        PaymentLinkSendErrorEnum.MANUAL_SEND_MESSAGE_ERROR  | "trip.biz.bff.paymentlinksend.error.manualSendMessage"
        PaymentLinkSendErrorEnum.PRODUCT_LINE_ERROR         | "trip.biz.bff.paymentlinksend.error.productline"
        PaymentLinkSendErrorEnum.MISS_EMAIL                 | "trip.biz.bff.paymentlinksend.error.missemail"
        PaymentLinkSendErrorEnum.EMAIL_CHECK_DATA_ERROR     | "trip.biz.bff.paymentlinksend.error.email"
    }

    def "test getFriendlyMessage with mocked BFFSharkUtil"() {
        given: "Mock BFFSharkUtil to return a predictable value"
        def mockSharkValue = "Mocked friendly message"
        new MockUp<BFFSharkUtil>() {
            @Mock
            String getSharkValue(String key) {
                return mockSharkValue
            }
        }

        when:
        String result = PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR.getFriendlyMessage()

        then:
        result == mockSharkValue
    }

    @Unroll
    def "test getFriendlyMessage calls BFFSharkUtil with correct key for #enumValue"() {
        given: "Mock BFFSharkUtil to capture the key parameter"
        String capturedKey = null
        new MockUp<BFFSharkUtil>() {
            @Mock
            String getSharkValue(String key) {
                capturedKey = key
                return "test message"
            }
        }

        when:
        enumValue.getFriendlyMessage()

        then:
        capturedKey == expectedSharkKey

        where:
        enumValue                                           | expectedSharkKey
        PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR  | "trip.biz.bff.paymentlinksend.error.param"
        PaymentLinkSendErrorEnum.MANUAL_SEND_MESSAGE_ERROR  | "trip.biz.bff.paymentlinksend.error.manualSendMessage"
        PaymentLinkSendErrorEnum.PRODUCT_LINE_ERROR         | "trip.biz.bff.paymentlinksend.error.productline"
        PaymentLinkSendErrorEnum.MISS_EMAIL                 | "trip.biz.bff.paymentlinksend.error.missemail"
        PaymentLinkSendErrorEnum.EMAIL_CHECK_DATA_ERROR     | "trip.biz.bff.paymentlinksend.error.email"
    }

    def "test enum implements IErrorInfo interface"() {
        expect: "All enum values should implement IErrorInfo"
        PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR instanceof com.ctrip.corp.bff.framework.template.common.exception.business.IErrorInfo
        PaymentLinkSendErrorEnum.MANUAL_SEND_MESSAGE_ERROR instanceof com.ctrip.corp.bff.framework.template.common.exception.business.IErrorInfo
        PaymentLinkSendErrorEnum.PRODUCT_LINE_ERROR instanceof com.ctrip.corp.bff.framework.template.common.exception.business.IErrorInfo
        PaymentLinkSendErrorEnum.MISS_EMAIL instanceof com.ctrip.corp.bff.framework.template.common.exception.business.IErrorInfo
        PaymentLinkSendErrorEnum.EMAIL_CHECK_DATA_ERROR instanceof com.ctrip.corp.bff.framework.template.common.exception.business.IErrorInfo
    }

    def "test enum values count"() {
        expect: "Should have exactly 5 enum values"
        PaymentLinkSendErrorEnum.values().length == 5
    }

    def "test enum constructor parameters"() {
        given:
        def enumValue = PaymentLinkSendErrorEnum.REQUEST_PARAM_CHECK_ERROR

        expect: "Constructor should properly initialize all fields"
        enumValue.getErrorCode() == 600
        enumValue.getErrorMessage() == "param error"
        enumValue.getSharkKey() == "trip.biz.bff.paymentlinksend.error.param"
    }
}