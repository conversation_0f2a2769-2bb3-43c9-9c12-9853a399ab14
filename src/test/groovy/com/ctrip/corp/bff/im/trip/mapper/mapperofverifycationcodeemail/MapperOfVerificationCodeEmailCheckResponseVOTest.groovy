package com.ctrip.corp.bff.im.trip.mapper.mapperofverifycationcodeemail

import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.im.contract.VerificationCodeEmailCheckResponseType
import com.ctrip.corp.bff.im.trip.contract.VerificationCodeEmailCheckResponseVO
import com.ctrip.corp.bff.im.trip.mapper.mapperofverificationcodecheck.MapperOfVerificationCodeEmailCheckResponseVO
import mockit.Mock
import mockit.MockUp
import spock.lang.Specification
import spock.lang.Unroll

class MapperOfVerificationCodeEmailCheckResponseVOTest extends Specification {
    def testObj = new MapperOfVerificationCodeEmailCheckResponseVO()

    def setup() {
            new MockUp<BFFSharkUtil>() {
                @Mock
                String getSharkValue(String key) {
                    return ""
                }
            }
        }

    @Unroll
    def "convertTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.convert(tuple1)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        tuple1 || expectedResult
        Tuple1.of(new VerificationCodeEmailCheckResponseType()) || new VerificationCodeEmailCheckResponseVO(checkSuccess: "F", checkToken: null, friendlyMessage: "")
        Tuple1.of(new VerificationCodeEmailCheckResponseType(checkResult: "T",checkToken: "11"))|| new VerificationCodeEmailCheckResponseVO(checkSuccess: "T", checkToken: "11")
    }

    @Unroll
    def "checkTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.check(tuple1)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        tuple1 || expectedResult
        null   || null
    }
}
